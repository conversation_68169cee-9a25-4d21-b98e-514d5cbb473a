<template>
  <div class="station-select-container">
    <!-- 导航栏 -->
    <van-nav-bar title="选择站点" fixed />

    <!-- 主要内容 -->
    <div class="content-wrapper">
      <!-- 刷新下拉 -->
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <!-- 站点列表 -->
        <div class="station-list">
          <van-cell-group>
            <van-cell
              v-for="station in stations"
              :key="station.id"
              :title="station.name"
              :label="`${station.projectName} | ${station.deviceModel} | ${station.sn}`"
              is-link
              @click="selectStation(station)"
            >
              <template #icon>
                <van-icon name="shop-o" size="20" color="#1989fa" />
              </template>
              <template #right-icon>
                <van-tag :type="station.isOnline ? 'success' : 'danger'">
                  {{ station.isOnline ? '在线' : '离线' }}
                </van-tag>
              </template>
            </van-cell>
          </van-cell-group>
        </div>
      </van-pull-refresh>

      <!-- 空状态 -->
      <van-empty
        v-if="stations.length === 0 && !loading"
        description="暂无可用站点"
        image="search"
      />

      <!-- 加载状态 -->
      <van-loading v-if="loading" type="spinner" color="#1989fa" vertical>
        加载站点信息...
      </van-loading>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import { StationService } from '@/services/stationService'
import type { StationDevice } from '@/types/station'

const router = useRouter()

// 站点数据接口定义
interface Station {
  id: string
  name: string
  sn: string
  location: string
  isOnline: boolean
  baseUrl: string
  projectName: string
  deviceModel: string
}

// 响应式数据
const loading = ref(false)
const refreshing = ref(false)
const stations = ref<Station[]>([])

/**
 * 选择站点并跳转
 * @param station 选中的站点
 */
function selectStation(station: Station) {
  showToast({
    message: `正在进入${station.name}...`,
    type: 'loading',
    duration: 1000
  })

  setTimeout(() => {
    router.push({
      name: 'device',
      params: {
        stationId: station.sn  // 使用SN作为stationId参数
      },
      query: {
        sn: station.sn,
        stationName: station.name,
        projectName: station.projectName,
        deviceModel: station.deviceModel,
        isOnline: station.isOnline.toString()
      }
    })
  }, 1000)
}

/**
 * 刷新站点列表
 */
async function onRefresh() {
  refreshing.value = true
  try {
    await fetchStations() // 复用fetchStations的逻辑（包含降级机制）
    showToast('刷新成功')
  } catch (error: unknown) {
    console.error('刷新站点列表失败:', error)
    showToast('刷新失败，请检查网络连接')
  } finally {
    refreshing.value = false
  }
}

/**
 * 获取站点列表（优先真实API，失败时降级到模拟数据）
 */
async function fetchStations() {
  loading.value = true
  try {
    // 尝试使用真实API获取数据
    const stationDevices = await StationService.getStationList()
    stations.value = stationDevices.map(transformToStation)

  } catch (error: unknown) {
    console.error('获取站点列表失败:', error)
    console.log('降级使用模拟数据')
    showToast('API获取失败，使用本地数据')
    // 降级到模拟数据
    stations.value = getMockStations()
  } finally {
    loading.value = false
  }
}

/**
 * 将StationDevice转换为Station格式
 */
function transformToStation(device: StationDevice): Station {
  return {
    id: device.deviceNo,
    name: device.deviceName || device.projectName,
    sn: device.sn,
    location: device.deviceAddress || device.projectName,
    isOnline: device.deviceStatus.onlineOffline === 1,
    baseUrl: 'http://**************:8500', // 使用现有的后端服务地址
    projectName: device.projectName,
    deviceModel: device.deviceModelName
  }
}

/**
 * 获取模拟数据（API调用失败时的降级方案）
 */
function getMockStations(): Station[] {
  return [
    {
      id: '02801925060700002997',
      name: '联丰村',
      sn: '02801925060700002997',
      location: '新盘门',
      isOnline: true,
      baseUrl: 'http://**************:8500',
      projectName: '新盘门',
      deviceModel: 'USR-M100-HMC1'
    }
  ]
}

onMounted(() => {
  fetchStations()
})
</script>

<style scoped>
.station-select-container {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.content-wrapper {
  padding-top: 46px; /* 为固定导航栏留出空间 */
  padding-bottom: 20px;
  min-height: calc(100vh - 66px);
}

.station-list {
  margin: 16px;
}

/* 自定义样式覆盖 */
:deep(.van-cell-group) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.van-loading) {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
}

:deep(.van-empty) {
  margin-top: 60px;
}

/* 移动端适配 */
@media (max-width: 480px) {
  .station-list {
    margin: 8px;
  }
}
</style>
